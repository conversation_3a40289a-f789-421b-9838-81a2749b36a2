"use client";

import { useEffect, useState, useRef, useCallback } from "react";
import dynamic from "next/dynamic";
import { PortfolioSummaryCard } from "@/components/dashboard/PortfolioSummaryCard";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { DashboardSkeleton } from "@/components/ui/advanced-skeleton";
import type { PortfolioAsset, PriceAlert } from "@/lib/types";
import {
  Activity,
  AlertTriangle,
  Loader2,
  RefreshCw,
  BarChart3,
  PieChart,
  Target,
  Zap,
  Settings,
  BellRing
} from "lucide-react";
import Link from "next/link";
import { toast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

// Import custom hooks
import { useApi<PERSON><PERSON>s } from "@/hooks/use-api-keys";
import { usePortfolioData } from "@/hooks/use-portfolio-data";
import { usePositionsData } from "@/hooks/use-positions-data";
import { useTransactionsData } from "@/hooks/use-transactions-data";
import { useNotificationHelpers } from "@/components/ui/notification-system";

// Dynamic imports para lazy loading de componentes pesados
const AssetTable = dynamic(() => import("@/components/dashboard/AssetTable").then(mod => ({ default: mod.AssetTable })), {
  loading: () => <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>,
  ssr: false
});

const TransactionHistoryTable = dynamic(() => import("@/components/dashboard/TransactionHistoryTable").then(mod => ({ default: mod.TransactionHistoryTable })), {
  loading: () => <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>,
  ssr: false
});

const OpenPositionsTable = dynamic(() => import("@/components/dashboard/OpenPositionsTable").then(mod => ({ default: mod.OpenPositionsTable })), {
  loading: () => <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>,
  ssr: false
});

const FuturesTradingTerminal = dynamic(() => import("@/components/trading/FuturesTradingTerminal").then(mod => ({ default: mod.FuturesTradingTerminal })), {
  loading: () => <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/><span className="ml-2">Loading Trading Terminal...</span></div>,
  ssr: false
});

const CreateAlertForm = dynamic(() => import("@/components/alerts/CreateAlertForm").then(mod => ({ default: mod.CreateAlertForm })), {
  loading: () => <div className="flex justify-center items-center py-4"><Loader2 className="h-6 w-6 animate-spin text-primary"/></div>,
  ssr: false
});

const PerformanceMetrics = dynamic(() => import("@/components/dashboard/PerformanceMetrics").then(mod => ({ default: mod.PerformanceMetrics })), {
  loading: () => <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>,
  ssr: false
});

const AdvancedPriceAlerts = dynamic(() => import("@/components/dashboard/AdvancedPriceAlerts").then(mod => ({ default: mod.AdvancedPriceAlerts })), {
  loading: () => <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>,
  ssr: false
});

const ActiveAlertsList = dynamic(() => import("@/components/alerts/ActiveAlertsList").then(mod => ({ default: mod.ActiveAlertsList })), {
  loading: () => <div className="flex justify-center items-center py-4"><Loader2 className="h-6 w-6 animate-spin text-primary"/></div>,
  ssr: false
});

interface AccountOverviewData {
  totalPortfolioValueUSDT: number;
  totalSpotValueUSDT: number;
  totalFuturesValueUSDT: number;
  spotPnl24hUSDT: number;
  spotPnl24hPercentage: number;
  assets: PortfolioAsset[];
  allPrices?: Record<string, string>;
}

interface CryptoInfo {
  symbol: string;
  name: string;
}

// Loading component for skeleton states - using imported DashboardSkeleton

// Error state component
const ErrorState = ({ error, onRetry }: { error: string; onRetry: () => void }) => (
  <Card className="alert-error">
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <AlertTriangle className="h-5 w-5" />
        Connection Error
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      <p className="text-sm">{error}</p>
      <div className="flex gap-2">
        <Button onClick={onRetry} size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
        <Button variant="outline" size="sm" asChild>
          <Link href="/settings">Settings</Link>
        </Button>
      </div>
    </CardContent>
  </Card>
);

export default function DashboardPage() {
  // Use custom hooks for data management
  const { apiKey, apiSecret, isLoading: isKeysLoading } = useApiKeys();

  const {
    data: portfolioData,
    isLoading: isLoadingAccountOverview,
    error: accountOverviewError,
    isRefreshing: isPortfolioRefreshing,
    refetch: refetchPortfolio
  } = usePortfolioData(apiKey, apiSecret);

  const {
    data: positionsData,
    isLoading: isLoadingOpenPositions,
    error: openPositionsError,
    isRefreshing: isPositionsRefreshing,
    refetch: refetchPositions
  } = usePositionsData(apiKey, apiSecret);

  const {
    data: recentTransactions,
    isLoading: isLoadingTransactions,
    error: transactionsError,
    isRefreshing: isTransactionsRefreshing,
    refetch: refetchTransactions
  } = useTransactionsData(apiKey, apiSecret);

  // Use notification helpers
  const { notifySuccess, notifyError, notifyDataUpdate } = useNotificationHelpers();

  // Extract data from hooks
  const accountOverview = portfolioData;
  const openPositions = positionsData?.positions || [];
  const totalUnrealizedFuturesPnl = positionsData?.totalUnrealizedFuturesPnlUSDT || 0;
  const currentPrices = portfolioData?.allPrices || {};

  // Local state for alerts and cryptocurrencies
  const [alerts, setAlerts] = useState<PriceAlert[]>([]);
  const [cryptocurrencies, setCryptocurrencies] = useState<CryptoInfo[]>([]);
  const [isLoadingCryptos, setIsLoadingCryptos] = useState(true);
  const [errorCryptos, setErrorCryptos] = useState<string | null>(null);
  const [activeAlertsCount, setActiveAlertsCount] = useState<number>(0);
  const alertsSectionRef = useRef<HTMLDivElement>(null);

  // Background refresh state
  const isBackgroundRefreshing = isPortfolioRefreshing || isPositionsRefreshing || isTransactionsRefreshing;

  // Manual refresh function that uses the hooks' refetch methods
  const handleManualRefresh = useCallback(async () => {
    if (apiKey && apiSecret) {
      try {
        await Promise.all([
          refetchPortfolio(),
          refetchPositions(),
          refetchTransactions()
        ]);
        notifyDataUpdate("Portfolio, Positions, and Transactions");
      } catch (error: any) {
        console.error('Manual refresh error:', error);
        notifyError("Refresh Failed", error?.message || "Failed to refresh data");
      }
    } else {
      notifyError("API Keys Missing", "Configure your API keys to refresh data");
    }
  }, [apiKey, apiSecret, refetchPortfolio, refetchPositions, refetchTransactions, notifyDataUpdate, notifyError]);

  // Fetch positions function for compatibility with existing components
  const fetchOpenPositions = useCallback(async () => {
    await refetchPositions();
  }, [refetchPositions]);

  // Fetch transactions function for compatibility with existing components
  const fetchRecentTransactions = useCallback(async () => {
    await refetchTransactions();
  }, [refetchTransactions]);

  const fetchCryptos = async () => {
    try {
      setIsLoadingCryptos(true);
      setErrorCryptos(null);
      const response = await fetch('/api/binance/exchange-info');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      if (result.success && Array.isArray(result.data)) {
        const formattedCryptos = result.data.map((symbol: any) => ({
          symbol: symbol.symbol,
          name: symbol.baseAsset === 'BTC' ? 'Bitcoin' : 
                symbol.baseAsset === 'ETH' ? 'Ethereum' :
                symbol.baseAsset === 'ADA' ? 'Cardano' :
                symbol.baseAsset === 'SOL' ? 'Solana' :
                symbol.baseAsset,
        }));
        setCryptocurrencies(formattedCryptos);
        setErrorCryptos(null);
      } else {
        setErrorCryptos('Formato de resposta inválido da API.');
        setCryptocurrencies([]);
      }
    } catch (err: any) {
      setErrorCryptos(`Erro ao buscar criptomoedas: ${err.message}`);
      setCryptocurrencies([]);
      console.error('Error fetching cryptos:', err);
    } finally {
      setIsLoadingCryptos(false);
    }
  };

  // Load alerts from localStorage and fetch cryptos
  useEffect(() => {
    const storedAlerts = localStorage.getItem('price-alerts');
    if (storedAlerts) {
      try {
        const parsedAlerts = JSON.parse(storedAlerts);
        setAlerts(Array.isArray(parsedAlerts) ? parsedAlerts : []);
      } catch (error) {
        console.error('Error parsing stored alerts:', error);
        setAlerts([]);
      }
    }
    fetchCryptos();
  }, []);

  // Update active alerts count
  useEffect(() => {
    const activeCount = alerts.filter(alert => alert.status === 'active').length;
    setActiveAlertsCount(activeCount);
  }, [alerts]);

  // The hooks handle auto-refresh automatically, no need for manual intervals

  const handleAlertCreated = (newAlertData: Omit<PriceAlert, 'id' | 'createdAt' | 'status'>) => {
    const newAlert: PriceAlert = {
      ...newAlertData,
      id: Date.now().toString(),
      status: 'active',
      createdAt: new Date(),
    };

    const updatedAlerts = [newAlert, ...alerts];
    setAlerts(updatedAlerts);
    localStorage.setItem('price-alerts', JSON.stringify(updatedAlerts));

    toast({
      title: "Alerta Criado",
      description: `Alerta criado para ${newAlertData.symbol}.`,
    });
  };

  const handleDeleteAlert = (alertId: string) => {
    const updatedAlerts = alerts.filter(alert => alert.id !== alertId);
    setAlerts(updatedAlerts);
    localStorage.setItem('price-alerts', JSON.stringify(updatedAlerts));

    toast({
      title: "Alerta Excluído",
      description: "O alerta de preço foi removido.",
    });
  };



  const handleScrollToAlerts = () => {
    alertsSectionRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  // Calculate derived state before any early returns
  const isAnyDataLoading = isLoadingAccountOverview || isLoadingOpenPositions || isLoadingTransactions;
  const hasAnyError = accountOverviewError || openPositionsError || transactionsError;

  if (isKeysLoading) {
    return <DashboardSkeleton />;
  }

  if (!apiKey || !apiSecret) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center space-y-6 p-8">
        <div className="relative">
          <AlertTriangle className="h-20 w-20 text-amber-500 animate-pulse" />
          <div className="absolute -top-2 -right-2 h-6 w-6 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-bold">!</span>
          </div>
        </div>
        <div className="space-y-2">
          <h2 className="text-3xl font-bold">API Configuration Required</h2>
          <p className="text-muted-foreground max-w-md">
            Please configure your Binance API keys to access your dashboard and start trading.
          </p>
        </div>
        <div className="flex gap-3">
          <Button asChild size="lg" className="btn-trading">
            <Link href="/settings">
              <Settings className="h-4 w-4 mr-2" />
              Configure API
            </Link>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <Link href="/onboarding">Learn More</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 md:space-y-6 px-2 sm:px-4 lg:px-0" key="dashboard-content-wrapper">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="space-y-1 min-w-0 flex-1">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight gradient-primary bg-clip-text text-transparent">
            Trading Dashboard
          </h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Real-time portfolio overview and trading insights
          </p>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          <Badge variant="outline" className="flex items-center gap-1 text-xs sm:text-sm">
            <div className={cn("status-dot", isBackgroundRefreshing ? "animate-pulse bg-amber-400" : "status-online")}></div>
            <span className="hidden sm:inline">{isBackgroundRefreshing ? "Updating..." : "Live Data"}</span>
            <span className="sm:hidden">{isBackgroundRefreshing ? "..." : "●"}</span>
          </Badge>
          <Button 
            onClick={handleManualRefresh} 
            variant="outline" 
            size="sm" 
            disabled={isAnyDataLoading}
            className="btn-trading text-xs sm:text-sm"
          >
            <RefreshCw className={cn("h-3 w-3 sm:h-4 sm:w-4", isAnyDataLoading ? "animate-spin" : "", "sm:mr-2")} />
            <span className="hidden sm:inline">Refresh</span>
          </Button>
        </div>
      </div>

      {/* Error State */}
      {hasAnyError && (
        <ErrorState 
          error="Failed to load some data. Please check your API configuration." 
          onRetry={handleManualRefresh}
        />
      )}

      {/* Portfolio Summary */}
      {accountOverview && (
        <PortfolioSummaryCard
          totalPortfolioValueUSDT={accountOverview.totalPortfolioValueUSDT}
          totalSpotValueUSDT={accountOverview.totalSpotValueUSDT}
          totalFuturesValueUSDT={accountOverview.totalFuturesValueUSDT}
          spotPnl24hUSDT={accountOverview.spotPnl24hUSDT}
          spotPnl24hPercentage={accountOverview.spotPnl24hPercentage}
          totalUnrealizedFuturesPnl={totalUnrealizedFuturesPnl}
          isLoading={isLoadingAccountOverview}
        />
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 h-auto transform-gpu">
          <TabsTrigger value="overview" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Overview</span>
            <span className="sm:hidden">View</span>
          </TabsTrigger>
          <TabsTrigger value="positions" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <Target className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Positions</span>
            <span className="sm:hidden">Pos</span>
          </TabsTrigger>
          <TabsTrigger value="trading" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <Zap className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Trading</span>
            <span className="sm:hidden">Trade</span>
          </TabsTrigger>
          <TabsTrigger value="alerts" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <BellRing className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Alerts</span>
            <span className="sm:hidden">Alert</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 md:space-y-6">
          {/* Performance Metrics */}
          <PerformanceMetrics
            portfolioData={portfolioData}
            positionsData={positionsData}
            isLoading={isLoadingAccountOverview || isLoadingOpenPositions}
          />

          {/* Assets Overview */}
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Asset Portfolio
              </CardTitle>
              <CardDescription>
                Your cryptocurrency holdings across all wallets
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingAccountOverview && !accountOverview?.assets?.length ? (
                <div className="flex justify-center items-center py-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary"/>
                </div>
              ) : accountOverviewError ? (
                <p className="text-destructive text-center py-10">{accountOverviewError}</p>
              ) : (
                <AssetTable assets={accountOverview?.assets || []} />
              )}
            </CardContent>
          </Card>

          {/* Transaction History */}
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Your latest trading transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingTransactions && !recentTransactions.length ? (
                <div className="flex justify-center items-center py-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary"/>
                </div>
              ) : transactionsError ? (
                <p className="text-destructive text-center py-10">{transactionsError}</p>
              ) : (
                <TransactionHistoryTable transactions={recentTransactions} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="positions" className="space-y-4 md:space-y-6">
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Open Positions
              </CardTitle>
              <CardDescription>
                Your active futures trading positions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingOpenPositions && !openPositions.length ? (
                <div className="flex justify-center items-center py-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary"/>
                </div>
              ) : openPositionsError ? (
                <p className="text-destructive text-center py-10">{openPositionsError}</p>
              ) : (
                <OpenPositionsTable 
                  positions={openPositions} 
                  apiKey={apiKey}
                  apiSecret={apiSecret}
                  onPositionUpdate={fetchOpenPositions}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trading" className="space-y-4 md:space-y-6">
          <FuturesTradingTerminal 
            availableCryptos={cryptocurrencies}
            activeAlerts={alerts.filter(alert => alert.status === 'active')}
            currentPrices={currentPrices}
            onScrollToAlerts={handleScrollToAlerts}
            apiKey={apiKey}
            apiSecret={apiSecret}
          />
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4 md:space-y-6">
          <div ref={alertsSectionRef}>
            <AdvancedPriceAlerts
              alerts={alerts}
              onCreateAlert={handleAlertCreated}
              onUpdateAlert={(id, updates) => {
                setAlerts(prev => prev.map(alert =>
                  alert.id === id ? { ...alert, ...updates } : alert
                ));
              }}
              onDeleteAlert={handleDeleteAlert}
              availableSymbols={cryptocurrencies.map(crypto => crypto.symbol)}
              currentPrices={currentPrices}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
